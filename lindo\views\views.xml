<odoo>
  <data>
    <!-- Basic menu structure for Lindo UI -->

    <!-- Top menu item -->
    <menuitem name="Lindo" id="lindo_menu_root" sequence="1"/>

    <!-- Menu categories -->
    <menuitem name="🎨 Enhanced Dashboard" id="lindo_menu_dashboard" parent="lindo_menu_root" sequence="10"/>
    <menuitem name="🧩 UI Components Demo" id="lindo_menu_ui_demo" parent="lindo_menu_root" sequence="20"/>
    <menuitem name="📱 Responsive Views" id="lindo_menu_responsive" parent="lindo_menu_root" sequence="25"/>
    <menuitem name="📚 Documentation" id="lindo_menu_docs" parent="lindo_menu_root" sequence="30"/>
    <menuitem name="⚙️ Settings" id="lindo_menu_settings" parent="lindo_menu_root" sequence="40"/>

    <!-- Simple menu items for navigation -->
    <menuitem name="🚀 Interactive Demo"
              id="lindo_menu_demo"
              parent="lindo_menu_ui_demo"
              sequence="10"/>

    <menuitem name="📖 UI Guide"
              id="lindo_menu_guide"
              parent="lindo_menu_docs"
              sequence="10"/>

    <menuitem name="🎨 Theme Settings"
              id="lindo_menu_theme"
              parent="lindo_menu_settings"
              sequence="10"/>

    <!-- Original commented templates for reference -->
    <!--
    <record model="ir.ui.view" id="lindo.list">
      <field name="name">Lindo list</field>
      <field name="model">lindo.lindo</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>

    <record model="ir.actions.act_window" id="lindo.action_window">
      <field name="name">Lindo window</field>
      <field name="res_model">lindo.lindo</field>
      <field name="view_mode">tree,form</field>
    </record>

    <record model="ir.actions.server" id="lindo.action_server">
      <field name="name">Lindo server</field>
      <field name="model_id" ref="model_lindo_lindo"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
    -->
  </data>
</odoo>
