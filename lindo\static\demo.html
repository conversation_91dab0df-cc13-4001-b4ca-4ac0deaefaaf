<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lindo Enhanced UI - Interactive Demo</title>
    <link rel="stylesheet" href="src/css/lindo_ui.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }

        .o_main_navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-size: 24px;
            font-weight: bold;
            color: white;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            gap: 20px;
            margin: 0;
            padding: 0;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .o_list_view {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }

        .o_list_table {
            width: 100%;
            border-collapse: collapse;
        }

        .o_list_table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .o_list_table thead th {
            color: white;
            font-weight: 600;
            border: none;
            padding: 16px 12px;
            text-align: left;
        }

        .o_list_table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .o_list_table tbody tr:hover {
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transform: translateX(5px);
        }

        .o_list_table tbody td {
            padding: 16px 12px;
            border: none;
            color: #333;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            color: #333;
        }

        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Enhanced Odoo Navigation Bar -->
    <nav class="o_main_navbar">
        <a href="#" class="navbar-brand">🚀 Lindo Enhanced</a>

        <ul class="navbar-nav">
            <li><a href="#dashboard" class="nav-link">Dashboard</a></li>
            <li><a href="#views" class="nav-link">Views</a></li>
            <li><a href="#forms" class="nav-link">Forms</a></li>
            <li><a href="#settings" class="nav-link">Settings</a></li>
        </ul>
    </nav>

    <div class="container">
        <div class="card fade-in">
            <h2>Enhanced Odoo UI Components</h2>
            <p>This demo showcases the enhanced Odoo UI components using native Odoo classes with modern styling improvements.</p>
        </div>

        <div class="card fade-in">
            <h2>Enhanced List View (.o_list_view)</h2>
            <p>Example of enhanced Odoo list view with gradient headers and hover effects:</p>
            <div class="o_list_view">
                <table class="o_list_table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Department</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Ahmed Ali</td>
                            <td><EMAIL></td>
                            <td>Sales</td>
                            <td>Active</td>
                        </tr>
                        <tr>
                            <td>Fatima Hassan</td>
                            <td><EMAIL></td>
                            <td>Marketing</td>
                            <td>Active</td>
                        </tr>
                        <tr>
                            <td>Omar Khalil</td>
                            <td><EMAIL></td>
                            <td>IT</td>
                            <td>Pending</td>
                        </tr>
                        <tr>
                            <td>Layla Mahmoud</td>
                            <td><EMAIL></td>
                            <td>HR</td>
                            <td>Active</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card fade-in">
            <h2>Enhanced Navigation (.o_main_navbar)</h2>
            <p>The navigation bar above demonstrates the enhanced styling with:</p>
            <ul>
                <li>✨ Modern gradient background</li>
                <li>🎯 Smooth hover effects</li>
                <li>📱 Responsive design</li>
                <li>⚡ CSS transitions</li>
            </ul>
        </div>

        <div class="card fade-in">
            <h2>Key Features</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h3>🎨 Native Enhancement</h3>
                    <p>Uses existing Odoo classes (.o_main_navbar, .o_list_view, etc.) with !important CSS overrides</p>
                </div>
                <div>
                    <h3>📱 Full Compatibility</h3>
                    <p>Works seamlessly with all Odoo views and maintains core functionality</p>
                </div>
                <div>
                    <h3>⚡ Enhanced UX</h3>
                    <p>Smooth animations, hover effects, and modern gradients</p>
                </div>
                <div>
                    <h3>🔧 Easy Integration</h3>
                    <p>Simple installation and automatic enhancement of existing components</p>
                </div>
            </div>
        </div>

        <div class="card fade-in">
            <h2>Installation</h2>
            <p>To use these enhancements in your Odoo installation:</p>
            <ol>
                <li>Install the Lindo module from the Apps menu</li>
                <li>The enhancements will be applied automatically</li>
                <li>All existing Odoo functionality remains unchanged</li>
                <li>Enjoy the improved user interface!</li>
            </ol>
            <a href="#" class="btn">Install Module</a>
            <a href="#" class="btn">View Documentation</a>
        </div>
    </div>

    <script>
        // Enhanced scroll effect for navbar
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.o_main_navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(102, 126, 234, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                navbar.style.backdropFilter = 'none';
            }
        });

        // Enhanced button animations
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px)';
                }, 100);
            });
        });

        // Enhanced table row animations
        document.querySelectorAll('.o_list_table tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
            });
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>